import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase, Profile } from '@/lib/supabase'
import { toast } from '@/components/ui/sonner'

interface AuthContextType {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  // Track the last user ID to prevent unnecessary profile fetches
  const lastUserIdRef = React.useRef<string | null>(null)

  const fetchProfile = useCallback(async (userId: string) => {
    try {
      console.log('🔍 [AuthContext] Fetching profile for user ID:', userId)
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('❌ [AuthContext] Error fetching profile:', error)
        // If profile doesn't exist, create a basic one
        if (error.code === 'PGRST116') {
          console.log('⚠️ [AuthContext] Profile not found, creating basic profile...')
          console.log('🚨 [AuthContext] WARNING: This may overwrite company assignment!')

          const { data: userData } = await supabase.auth.getUser()
          if (userData.user) {
            // Check if user metadata has company info
            const userMetadata = userData.user.user_metadata || {}
            console.log('📋 [AuthContext] User metadata:', userMetadata)

            const { data: newProfile, error: createError } = await supabase
              .from('profiles')
              .insert({
                id: userId,
                email: userData.user.email || '',
                role: userMetadata.role || 'user',
                company_id: userMetadata.company_id || null
              })
              .select()
              .single()

            if (createError) {
              console.error('❌ [AuthContext] Error creating profile:', createError)
              return null
            }

            console.log('✅ [AuthContext] Created new profile:', newProfile)
            return newProfile
          }
        }
        return null
      }

      console.log('✅ [AuthContext] Profile fetched successfully:', data)
      return data
    } catch (error) {
      console.error('❌ [AuthContext] Error in fetchProfile:', error)
      return null
    }
  }, [])

  const refreshProfile = async () => {
    if (user) {
      const profileData = await fetchProfile(user.id)
      setProfile(profileData)
    }
  }

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)

      if (session?.user) {
        lastUserIdRef.current = session.user.id
        fetchProfile(session.user.id).then(setProfile)
      }

      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Auth state changed:', event, session?.user?.id, 'lastUserId:', lastUserIdRef.current)
      }

      setSession(session)
      setUser(session?.user ?? null)

      // Only fetch profile if user ID has actually changed
      if (session?.user) {
        if (session.user.id !== lastUserIdRef.current) {
          lastUserIdRef.current = session.user.id
          const profileData = await fetchProfile(session.user.id)
          setProfile(profileData)
        } else if (process.env.NODE_ENV === 'development') {
          console.log('User ID unchanged, skipping profile fetch')
        }
      } else {
        lastUserIdRef.current = null
        setProfile(null)
      }

      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, []) // Remove fetchProfile from dependency array

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        toast.error(error.message)
        return { error }
      }

      toast.success('Successfully signed in!')
      return { error: null }
    } catch (error) {
      const authError = error as AuthError
      toast.error('An unexpected error occurred')
      return { error: authError }
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        toast.error(error.message)
      } else {
        toast.success('Successfully signed out!')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    }
  }

  const value: AuthContextType = {
    user,
    profile,
    session,
    loading,
    signIn,
    signOut,
    refreshProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

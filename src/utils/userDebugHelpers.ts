import { supabase } from '@/lib/supabase'

export interface UserDebugInfo {
  totalUsers: number
  users: any[]
  authUsers: any[]
  profilesWithoutAuth: any[]
  authWithoutProfiles: any[]
}

/**
 * Debug helper to analyze user data discrepancies
 */
export const debugUserData = async (): Promise<UserDebugInfo> => {
  console.log('🔍 Starting user data debug analysis...')
  
  try {
    // Fetch all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false })

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError)
      throw profilesError
    }

    console.log('📊 Profiles found:', profiles?.length || 0)
    console.log('📋 Profiles data:', profiles)

    // Try to get auth users (this might not work from client side)
    let authUsers: any[] = []
    try {
      // This will only return the current user from client side
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        authUsers = [user]
      }
    } catch (error) {
      console.log('Cannot fetch all auth users from client side')
    }

    // Analyze data
    const profileIds = new Set(profiles?.map(p => p.id) || [])
    const authIds = new Set(authUsers.map(u => u.id))

    const profilesWithoutAuth = profiles?.filter(p => !authIds.has(p.id)) || []
    const authWithoutProfiles = authUsers.filter(u => !profileIds.has(u.id))

    const debugInfo: UserDebugInfo = {
      totalUsers: profiles?.length || 0,
      users: profiles || [],
      authUsers,
      profilesWithoutAuth,
      authWithoutProfiles
    }

    console.log('🔍 Debug analysis complete:', debugInfo)
    return debugInfo

  } catch (error) {
    console.error('❌ Debug analysis failed:', error)
    throw error
  }
}

/**
 * Test user creation process
 */
export const testUserCreation = async (email: string, password: string, role: 'user' | 'super_admin', companyId?: string) => {
  console.log('🧪 Testing user creation process...')
  
  try {
    // Step 1: Create auth user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: undefined,
        data: {
          role,
          company_id: companyId || null
        }
      }
    })

    if (authError) {
      console.error('❌ Auth creation failed:', authError)
      return { success: false, error: authError.message }
    }

    console.log('✅ Auth user created:', authData.user?.id)

    if (!authData.user) {
      return { success: false, error: 'No user returned from auth' }
    }

    // Step 2: Create/update profile
    const profileData = {
      id: authData.user.id,
      email,
      role,
      company_id: companyId || null
    }

    const { error: profileError } = await supabase
      .from('profiles')
      .upsert(profileData, { onConflict: 'id' })

    if (profileError) {
      console.error('❌ Profile creation failed:', profileError)
      return { success: false, error: profileError.message }
    }

    console.log('✅ Profile created/updated')

    // Step 3: Verify profile
    const { data: verifyProfile, error: verifyError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()

    if (verifyError) {
      console.error('❌ Profile verification failed:', verifyError)
      return { success: false, error: verifyError.message }
    }

    console.log('✅ Profile verified:', verifyProfile)

    return {
      success: true,
      userId: authData.user.id,
      profile: verifyProfile
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Check for RLS policy issues
 */
export const checkRLSAccess = async () => {
  console.log('🔒 Checking RLS access...')
  
  try {
    // Test basic read access
    const { data, error, count } = await supabase
      .from('profiles')
      .select('*', { count: 'exact' })

    if (error) {
      console.error('❌ RLS access denied:', error)
      return { success: false, error: error.message }
    }

    console.log('✅ RLS access granted, count:', count, 'data length:', data?.length)
    
    return {
      success: true,
      count,
      dataLength: data?.length || 0,
      hasDiscrepancy: count !== (data?.length || 0)
    }

  } catch (error) {
    console.error('❌ RLS check failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}
